namespace KeyCabs.BusinessDataContextMigration
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;

    internal sealed class Configuration : DbMigrationsConfiguration<KeyCabs.Models.BusinessDataContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = false;
            MigrationsDirectory = @"BusinessDataContextMigration";
        }

        protected override void Seed(KeyCabs.Models.BusinessDataContext context)
        {
            //  This method will be called after migrating to the latest version.

            //  You can use the DbSet<T>.AddOrUpdate() helper extension method 
            //  to avoid creating duplicate seed data. E.g.
            //
            //    context.People.AddOrUpdate(
            //      p => p.FullName,
            //      new Person { FullName = "<PERSON> Peters" },
            //      new Person { FullName = "<PERSON><PERSON> Lambson" },
            //      new Person { FullName = "<PERSON> Miller" }
            //    );
            //
        }
    }
}
