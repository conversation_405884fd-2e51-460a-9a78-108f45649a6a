﻿using System.Web;
using System.Web.Optimization;

namespace KeyCabs
{
    public class BundleConfig
    {
        // 有关绑定的详细信息，请访问 http://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery-{version}.js"));
            bundles.Add(new ScriptBundle("~/bundles/easyui").Include(
                        "~/Scripts/jquery.easyui.min-{version}.js",
                        "~/Scripts/locale/easyui-lang-zh_CN.js",
                        "~/Scripts/pcasunzip.js",
                        "~/Scripts/pg.js"));
            bundles.Add(new ScriptBundle("~/bundles/easyuivalidate").Include(
                        "~/Scripts/jquery.easyui.validate.js", 
                        "~/Scripts/locale/easyui-lang-zh_CN.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Scripts/jquery.validate*"));
            bundles.Add(new ScriptBundle("~/bundles/cookie").Include(
                      "~/Scripts/jquery.cookie.js"));
            bundles.Add(new ScriptBundle("~/bundles/changeTheme").Include(
                      "~/Scripts/changeEasyuiTheme.js"));
            bundles.Add(new ScriptBundle("~/bundles/highcharts").Include(
                      "~/Scripts/highcharts/highcharts.js",
                      "~/Scripts/highcharts/highcharts-more.js",
                      "~/Scripts/highcharts/solid-gauge.js"
                        ));
            // 使用要用于开发和学习的 Modernizr 的开发版本。然后，当你做好
            // 生产准备时，请使用 http://modernizr.com 上的生成工具来仅选择所需的测试。
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.js",
                      "~/Scripts/respond.js"));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bootstrap.css",
                      "~/Content/site.css"));
            //easyui CSS 路径设置
            bundles.Add(new StyleBundle("~/Content/easyui").Include(
                      "~/Content/themes/default/easyui.css",
                      "~/Content/themes/icon.css",
                      "~/Content/themes/IconExtension.css"));
            BundleTable.EnableOptimizations = false;
        }
    }
}
