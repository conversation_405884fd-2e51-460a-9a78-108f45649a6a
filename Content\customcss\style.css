﻿@charset "utf-8";
/* CSS Document */
/***
 * uimaker
 * http://www.uimaker.com
 * e-mail: <EMAIL>
 */
*{font-size:9pt;border:0;margin:0;padding:0;}
body{font-family:'微软雅黑'; margin:0 auto;min-width:980px;}
ul{display:block;margin:0;padding:0;list-style:none;}
li{display:block;margin:0;padding:0;list-style: none;}
img{border:0;}
dl,dt,dd,span{margin:0;padding:0;display:block;}
a,a:focus{text-decoration:none;color:#000;outline:none;blr:expression(this.onFocus=this.blur());}
a:hover{color:#00a4ac;text-decoration:none;}
table{border-collapse:collapse;border-spacing: 0;}
cite{font-style:normal;}
h2{font-weight:normal;}

/*cloud*/

#mainBody {width:100%;height:100%;position:absolute;z-index:-1;}
.cloud {position:absolute;top:0px;left:0px;width:100%;height:100%;background:url(/images/login/cloud.png) no-repeat;z-index:1;opacity:0.5;}
#cloud2 {z-index:2;}


/*login*/
.logintop{height:47px; position:absolute; top:0; background:url(/images/login/loginbg1.png) repeat-x;z-index:100; width:100%;}
.logintop span{color:#fff; line-height:47px; background:url(/images/login/loginsj.png) no-repeat 21px 18px; text-indent:44px; color:#afc5d2; float:left;}
.logintop ul{float:right; padding-right:30px;}
.logintop ul li{float:left; margin-left:20px; line-height:47px;}
.logintop ul li a{color:#afc5d2;}
.logintop ul li a:hover{color:#fff;}
.loginbody{background:url(/images/login/loginbg3.png) no-repeat center center; width:100%; height:585px; overflow:hidden; position:absolute; top:47px;}
.systemlogo{background:url(/images/login/loginlogo.png) no-repeat center;width:100%; height:184px; margin-top:45px;}
.loginbox{width:692px; height:336px; background:url(/images/login/logininfo.png) no-repeat; margin-top:10px;}
.loginbox ul{margin-top:88px; margin-left:285px;}
.loginbox ul li{margin-bottom:25px;}
.loginbox ul li label{color:#687f92; padding-left:25px;}
.loginbox ul li label a{color:#687f92;}
.loginbox ul li label a:hover{color:#3d96c9;}
.loginbox ul li label input{margin-right:5px;}
.loginuser{width:299px; height:48px; background:url(/images/login/loginuser.png) no-repeat; border:none; line-height:48px; padding-left:44px; font-size:14px; font-weight:bold;}
.loginpwd{width:299px; height:48px; background:url(/images/login/loginpassword.png) no-repeat; border:none;line-height:48px; padding-left:44px; font-size:14px; color:#90a2bc;}
.loginbtn{width:111px;height:35px; background:url(/images/login/buttonbg.png) repeat-x; font-size:14px; font-weight:bold; color:#fff;cursor:pointer; line-height:35px;}
.loginbm{height:50px; line-height:50px; text-align:center; background:url(/images/login/loginbg2.png) repeat-x;position:absolute; bottom:0; width:100%; color:#0b3a58;}
.loginbm a{font-weight:bold;color:#0b3a58;}
.loginbm a:hover{color:#fff;}




/*default*/
.mainbox{padding:8px;position:relative;}
.mainleft{padding-right:298px;}
.leftinfo{border:#d3dbde solid 1px; height:290px;}
.mainright{width:298px;position:absolute; top:8px; right:8px;}
.dflist{border:#d3dbde solid 1px; width:288px; height:290px; float:right;}
.dflist1{border:#d3dbde solid 1px; width:288px; height:238px; float:right; margin-top:8px;}
.listtitle{background:url(/images/login/tbg.png) repeat-x; height:36px; line-height:36px; border-bottom:solid 1px #d3dbde; text-indent:14px; font-weight:bold; font-size:14px;}
.more1{float:right; font-weight:normal;color:#307fb1; padding-right:17px;}
.maintj{text-align:center;}
.newlist{padding-left:14px; padding-top:15px;}
.newlist li{line-height:25px; background:url(/images/login/list2.png) no-repeat 0px 8px; text-indent:11px;}
.newlist i{width:80px; display:block; float:left; font-style:normal;}
.newlist b{font-weight:normal; color:#7b7b7b; padding-left:10px;}
.leftinfos{height:238px;margin-top:8px;}
.infoleft{border:#d3dbde solid 1px; float:left;height:238px;}
.inforight{border:#d3dbde solid 1px; float:right;height:238px; }
.tooli{padding:30px 20px;}
.tooli li{float:left;padding-left:15px; padding-right:15px;margin-bottom:20px;}
.tooli li span{text-align:center;}
.tooli li p{line-height:35px; text-align:center;}



/*error 404*/
.error{background:url(/images/login/404.png) no-repeat; width:490px; margin-top:75px;padding-top:65px;}
.error h2{font-size:22px; padding-left:154px;}
.error p{padding-left:154px; line-height:35px;color:#717678;}
.reindex{padding-left:154px;}
.reindex a{width:115px; height:35px; font-size:14px; font-weight:bold; color:#fff; background:#3c95c8; display:block; line-height:35px; text-align:center;border-radius: 3px; behavior:url(js/pie.htc);margin-top:20px;}

